/* Trading Dashboard - Main Container */
.trading-dashboard {
  min-height: 100vh;
  background: #1A1A1A;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  padding: 120px 40px 40px;
  position: relative;
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 40px;
  max-width: 1400px;
  margin-left: auto;
  margin-right: auto;
}

.go-back-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.go-back-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.dashboard-main-title {
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
}

/* Progress Bar */
.progress-container {
  max-width: 1400px;
  margin: 0 auto 40px;
}

.progress-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 40px;
  padding: 24px;
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(20px);
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  position: relative;
}

.progress-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20px;
  right: -40px;
  width: 40px;
  height: 2px;
  background: rgba(255, 255, 255, 0.2);
}

.progress-step.completed:not(:last-child)::after {
  background: #BF4129;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  transition: all 0.3s ease;
}

.progress-step.completed .step-number {
  background: #BF4129;
  border-color: #BF4129;
}

.progress-step.active .step-number {
  background: #BF4129;
  border-color: #BF4129;
  box-shadow: 0 0 20px rgba(191, 65, 41, 0.4);
}

.step-label {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.7);
  text-align: center;
  max-width: 120px;
}

.progress-step.completed .step-label,
.progress-step.active .step-label {
  color: #FFFFFF;
}

/* Success Alert */
.success-alert {
  max-width: 1400px;
  margin: 0 auto 40px;
}

.alert-content {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px 24px;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.3);
  border-radius: 12px;
  position: relative;
}

.alert-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.alert-text h4 {
  font-size: 16px;
  font-weight: 600;
  color: #22C55E;
  margin: 0 0 8px 0;
}

.alert-text p {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  line-height: 1.5;
}

.alert-close {
  position: absolute;
  top: 16px;
  right: 16px;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  font-size: 20px;
  cursor: pointer;
  padding: 4px;
  transition: color 0.3s ease;
}

.alert-close:hover {
  color: #FFFFFF;
}

/* Dashboard Content */
.dashboard-content {
  max-width: 1400px;
  margin: 0 auto;
}

/* Dashboard Stats */
.dashboard-stats {
  margin-bottom: 60px;
}

.dashboard-title {
  font-size: 28px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 32px 0;
  text-align: center;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.stat-card {
  background: rgba(26, 26, 26, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(20px);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  border-color: rgba(191, 65, 41, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.stat-header {
  margin-bottom: 16px;
}

.stat-label {
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #FFFFFF;
  margin-bottom: 8px;
}

.stat-change {
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.stat-change.positive {
  color: #22C55E;
}

.stat-change.negative {
  color: #EF4444;
}

/* More Than Trading Section */
.more-than-trading {
  background: linear-gradient(135deg, rgba(191, 65, 41, 0.1) 0%, rgba(209, 144, 73, 0.1) 100%);
  border: 1px solid rgba(191, 65, 41, 0.2);
  border-radius: 24px;
  padding: 48px;
  text-align: center;
  max-width: 1400px;
  margin: 0 auto;
}

.more-than-trading-title {
  font-size: 36px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 24px 0;
  background: linear-gradient(135deg, #BF4129 0%, #D19049 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.more-than-trading-text {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto 40px;
}

.more-than-trading-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.start-staking-btn {
  padding: 16px 32px;
  background: linear-gradient(135deg, #BF4129 0%, #D19049 100%);
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(191, 65, 41, 0.3);
}

.start-staking-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(191, 65, 41, 0.4);
}

.get-funded-btn {
  padding: 16px 32px;
  background: transparent;
  border: 2px solid #BF4129;
  border-radius: 12px;
  color: #BF4129;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.get-funded-btn:hover {
  background: #BF4129;
  color: #FFFFFF;
  transform: translateY(-2px);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .trading-dashboard {
    padding: 100px 20px 20px;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .dashboard-main-title {
    font-size: 24px;
  }

  .progress-bar {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
  }

  .progress-step:not(:last-child)::after {
    display: none;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .stat-card {
    padding: 20px;
  }

  .stat-value {
    font-size: 24px;
  }

  .more-than-trading {
    padding: 32px 24px;
  }

  .more-than-trading-title {
    font-size: 28px;
  }

  .more-than-trading-buttons {
    flex-direction: column;
    align-items: center;
  }

  .start-staking-btn,
  .get-funded-btn {
    width: 100%;
    max-width: 280px;
  }
}
