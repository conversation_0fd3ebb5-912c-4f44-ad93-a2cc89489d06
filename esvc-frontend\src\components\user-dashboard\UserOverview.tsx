import React, { useState } from 'react';
import '../../styles/components/user-dashboard/UserOverview.css';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';
import DashboardModeSelector from '../DashboardModeSelector';
import { useDemoState } from '../../context/DemoStateContext';

// Import icons
import trendUpIcon from '../../assets/trend-up.png';
import informationCircleIcon from '../../assets/information-circle.png';
import cardCoinIcon from '../../assets/card-coin.png';
import exclamationIcon from '../../assets/Exclamation _Icon.png';
import tickCircleIcon from '../../assets/tick-circle.png';

interface UserOverviewProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const UserOverview: React.FC<UserOverviewProps> = ({
  onNavigateToSignUp,
  onNavigateToLogin,
  onNavigateToLanding
}) => {
  const { userData, updateUserData } = useDemoState();
  const [activeTab, setActiveTab] = useState('overview');
  const [showBalances, setShowBalances] = useState(true);
  const [hideBalances, setHideBalances] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [showMinimumModal, setShowMinimumModal] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [withdrawAmount, setWithdrawAmount] = useState('');
  const [walletAddress, setWalletAddress] = useState('');

  // Use demo data
  const userEarnings = userData.totalEarned;
  const availableForWithdrawal = userData.availableForWithdrawal;
  const minimumWithdrawal = 10; // Minimum withdrawal amount

  // Toggle balance visibility
  const toggleBalances = () => {
    setShowBalances(!showBalances);
    setHideBalances(!hideBalances);
  };

  // Handle withdraw button click
  const handleWithdrawClick = () => {
    if (availableForWithdrawal < minimumWithdrawal) {
      setShowMinimumModal(true);
    } else {
      setShowWithdrawModal(true);
    }
  };

  // Handle withdrawal submission
  const handleWithdrawSubmit = () => {
    const amount = parseFloat(withdrawAmount);
    if (amount >= minimumWithdrawal && amount <= availableForWithdrawal && walletAddress.trim()) {
      setShowWithdrawModal(false);
      setShowSuccessModal(true);
      // Reset form
      setWithdrawAmount('');
      setWalletAddress('');
    }
  };

  // Close modals
  const closeModals = () => {
    setShowWithdrawModal(false);
    setShowMinimumModal(false);
    setShowSuccessModal(false);
  };

  return (
    <UserDashboardLayout
      className="user-overview-container"
      hideNavAndFooter={showWithdrawModal || showMinimumModal || showSuccessModal}
    >
      <div className="user-overview-content">
        {/* User Greeting Header */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, {userData.name} 👋</h1>
            <p className="greeting-subtitle">Here is your staking overview</p>

            <div className="header-controls">
              <button className="stake-esvc-btn">
                <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
                Stake ESVC
              </button>

              <div className="balance-toggle">
                <span className="toggle-label">Show balances</span>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={showBalances}
                    onChange={toggleBalances}
                  />
                  <span className="toggle-slider"></span>
                </label>
                <span className="toggle-label">Hide balances</span>
              </div>
            </div>
          </div>
        </div>



        <div className="dashboard-layout">
          {/* Dashboard Mode Selector */}
          <div className="dashboard-mode-tabs">
            <DashboardModeSelector />
          </div>

          {/* Dashboard Content Wrapper */}
          <div className="dashboard-content-wrapper">
            {/* Sidebar */}
            <UserSideNav
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />

            {/* Dashboard Content */}
            <div className="dashboard-content">
            {/* General Summary */}
            <div className="summary-section">
              <h2 className="section-title">General Summary (From Your 7 Wallets)</h2>

              <div className="summary-grid">
                <div className="summary-card">
                  <div className="card-header">
                    <span className="card-label">TOTAL ESVC STAKED</span>
                  </div>
                  <div className="card-value">
                    {showBalances ? userData.totalStaked.toFixed(2) : '***.**'}
                    <span className="card-unit">ESVC</span>
                  </div>
                  <div className="card-header">
                    <span className="card-label">CURRENT STAKED ESVC VALUE</span>
                  </div>
                  <div className="card-value">
                    {showBalances ? `$${userData.currentValue.toLocaleString()}` : '$***,***'}
                  </div>
                </div>

                <div className="summary-card">
                  <div className="card-header">
                    <span className="card-label">ROI EARNED SO FAR</span>
                  </div>
                  <div className="card-value">
                    {showBalances ? '$700' : '$***'}
                  </div>
                  <div className="card-change positive">
                    <img src={trendUpIcon} alt="Trend up" className="change-icon" />
                    + 4.8% Today
                  </div>
                </div>

                <div className="summary-card">
                  <div className="card-header">
                    <span className="card-label">ROI PAID SO FAR</span>
                  </div>
                  <div className="card-value">
                    {showBalances ? '$660' : '$***'}
                  </div>
                </div>
              </div>
            </div>

            {/* Individual Wallet Summary */}
            <div className="wallet-summary">
              <h2 className="section-title">Individual Wallet Summary</h2>

              <div className="wallet-header-row">
                <div className="wallet-selector">
                  <select className="wallet-dropdown">
                    <option value="wallet1">🔒 Wallet 1 ($10,000 stakes)</option>
                  </select>
                </div>

                <div className="wallet-info">
                  <img src={informationCircleIcon} alt="Info" className="info-icon" />
                  <span className="info-text">
                    Every new stake you make gets its own dedicated wallet for added transparency and traceability.
                  </span>
                </div>
              </div>

              <div className="wallet-stats-grid">
                <div className="wallet-stat-card">
                  <div className="card-header">
                    <span className="card-label">TOTAL ESVC STAKED</span>
                  </div>
                  <div className="card-content">
                    <div className="card-value">
                      {showBalances ? '788.50' : '***.**'}
                      <span className="card-unit">ESVC</span>
                    </div>
                    <div className="card-change positive">
                      <img src={trendUpIcon} alt="Trend up" className="change-icon" />
                      + 4.8%
                    </div>
                  </div>
                </div>

                <div className="wallet-stat-card">
                  <div className="card-header">
                    <span className="card-label">CURRENT STAKED ESVC VALUE</span>
                  </div>
                  <div className="card-content">
                    <div className="card-value">
                      {showBalances ? '$13,700' : '$***,***'}
                    </div>
                    <div className="card-change positive">
                      <img src={trendUpIcon} alt="Trend up" className="change-icon" />
                      + 4.8%
                    </div>
                    <div className="card-note">$10,000 at Deposit</div>
                  </div>
                </div>

                <div className="wallet-stat-card">
                  <div className="card-content-dual">
                    <div className="card-section">
                      <div className="card-header">
                        <span className="card-label">ROI EARNED SO FAR</span>
                      </div>
                      <div className="card-value">
                        {showBalances ? '$700' : '$***'}
                      </div>
                    </div>
                    <div className="card-section">
                      <div className="card-header">
                        <span className="card-label">ROI PAID SO FAR</span>
                      </div>
                      <div className="card-value">
                        {showBalances ? '$660' : '$***'}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* ROI Summary - Inside the wallet card */}
              <div className="roi-section">
                <div className="roi-header">
                  <h3 className="roi-title">ROI Summary</h3>
                  <div className="roi-available">${showBalances ? '8.3' : '***'} ({showBalances ? '11.2832' : '***'} ESVC) Available</div>
                </div>

                <div className="roi-progress">
                  <div className="progress-bar">
                    <div className="progress-fill" style={{ width: '35%' }}></div>
                  </div>
                  <div className="progress-labels">
                    <span className="progress-earned">${showBalances ? '700' : '***'} ROI earned so far</span>
                    <span className="progress-expected">${showBalances ? '2,000' : '***'} Total Expected ROI</span>
                  </div>
                </div>

                <button className="withdraw-roi-btn" onClick={handleWithdrawClick}>Withdraw Earned ROI</button>

                {/* ROI Info - Inside the ROI section */}
                <div className="roi-info">
                  <img src={informationCircleIcon} alt="Info" className="info-icon" />
                  <span className="info-text">Minimum withdrawal amount is $10</span>
                </div>
              </div>

              {/* Unstake Section - Inside the wallet card, no separate card */}
              <div className="unstake-section-inner">
                <h3 className="unstake-title">Unstake</h3>

                <div className="unstake-grid">
                  <div className="unstake-info">
                    <div className="unstake-item">
                      <span className="unstake-label">DATE STAKED</span>
                      <span className="unstake-value">Jan 3, 2025</span>
                    </div>
                    <div className="unstake-item">
                      <span className="unstake-label">UNSTAKE DATE</span>
                      <span className="unstake-value">Jan 3, 2026</span>
                    </div>
                    <div className="unstake-item">
                      <span className="unstake-label">DAYS LEFT UNTIL UNSTAKE</span>
                      <span className="unstake-value">267 Days</span>
                    </div>
                  </div>

                  <button className="unstake-btn">Unstake</button>
                </div>
              </div>
            </div>
          </div>
          </div>
        </div>
      </div>

      {/* Withdrawal Modal */}
      {showWithdrawModal && (
        <div className="modal-overlay" onClick={closeModals}>
          <div className="modal-content" onClick={(e) => e.stopPropagation()}>
            <div className="modal-header">
              <h2 className="modal-title">Withdraw Your Earned ROI</h2>
              <button className="modal-close" onClick={closeModals}>×</button>
            </div>

            <div className="modal-body">
              <p className="modal-description">
                Withdrawals are processed in USDC. ROI is updated daily and available once your total earnings reach $10.
              </p>

              <div className="withdrawal-stats">
                <div className="stat-item">
                  <img src={cardCoinIcon} alt="Wallet" className="stat-icon" />
                  <span className="stat-label">Wallet | {userEarnings} staked</span>
                </div>

                <div className="stat-row">
                  <div className="stat-column">
                    <span className="stat-title">TOTAL ROI EARNED</span>
                    <span className="stat-value">${userEarnings}</span>
                  </div>
                  <div className="stat-column">
                    <span className="stat-title">ROI AVAILABLE FOR WITHDRAWAL</span>
                    <span className="stat-value">${availableForWithdrawal}</span>
                  </div>
                </div>

                <div className="eligibility-status">
                  <div className="status-icon success">
                    <img src={tickCircleIcon} alt="Success" />
                  </div>
                  <span className="status-text">You are eligible to withdraw from this wallet.</span>
                </div>
              </div>

              <div className="form-section">
                <label className="form-label">Enter your USDC wallet address for payout</label>
                <div className="input-group">
                  <input
                    type="text"
                    className="form-input"
                    placeholder="Paste 0"
                    value={walletAddress}
                    onChange={(e) => setWalletAddress(e.target.value)}
                  />
                </div>
                <p className="form-note">
                  <img src={informationCircleIcon} alt="Info" className="note-icon" />
                  Ensure your wallet supports USDC (Solana Network).
                </p>
              </div>
            </div>

            <div className="modal-footer">
              <button
                className="withdraw-submit-btn"
                onClick={handleWithdrawSubmit}
                disabled={!walletAddress.trim() || parseFloat(withdrawAmount) < minimumWithdrawal}
              >
                Withdraw Now
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Minimum Amount Modal */}
      {showMinimumModal && (
        <div className="modal-overlay" onClick={closeModals}>
          <div className="modal-content minimum-modal" onClick={(e) => e.stopPropagation()}>
            <button className="modal-close" onClick={closeModals}>×</button>
            <div className="modal-icon">
              <img src={exclamationIcon} alt="Warning" className="warning-icon" />
            </div>
            <h2 className="modal-title">Minimum withdrawal amount is $10</h2>
            <p className="modal-description">Please accumulate more earnings before withdrawing.</p>
            <button className="modal-button" onClick={closeModals}>Got It!</button>
          </div>
        </div>
      )}

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="modal-overlay" onClick={closeModals}>
          <div className="modal-content success-modal" onClick={(e) => e.stopPropagation()}>
            <button className="modal-close" onClick={closeModals}>×</button>
            <div className="modal-icon">
              <img src={tickCircleIcon} alt="Success" className="success-icon" />
            </div>
            <h2 className="modal-title">Withdrawal Request Received!</h2>
            <p className="modal-description">
              Your USDC payout will be sent to the wallet provided. Track your withdrawal in your Transaction History.
            </p>
            <button className="modal-button" onClick={closeModals}>Got It!</button>
          </div>
        </div>
      )}
    </UserDashboardLayout>
  );
};

export default UserOverview;
