import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import Layout from './Layout';
import '../styles/components/TradingDashboard.css';

interface TradingDashboardProps {}

const TradingDashboard: React.FC<TradingDashboardProps> = () => {
  const navigate = useNavigate();
  const [showSuccessAlert, setShowSuccessAlert] = useState(true);

  const handleGoBack = () => {
    navigate('/trade-challenge');
  };

  const handleCloseAlert = () => {
    setShowSuccessAlert(false);
  };

  const renderProgressBar = () => (
    <div className="progress-container">
      <div className="progress-bar">
        <div className="progress-step completed">
          <span className="step-number">1</span>
          <span className="step-label">PAY ONE-TIME FEE</span>
        </div>
        <div className="progress-step completed">
          <span className="step-number">2</span>
          <span className="step-label">LINK YOUR TRADING ACCOUNT</span>
        </div>
        <div className="progress-step completed active">
          <span className="step-number">3</span>
          <span className="step-label">YOUR TRADING DASHBOARD</span>
        </div>
      </div>
    </div>
  );

  const renderSuccessAlert = () => (
    <div className="success-alert">
      <div className="alert-content">
        <div className="alert-icon">✅</div>
        <div className="alert-text">
          <h4>You've Successfully Joined the Trade Challenge!</h4>
          <p>We'll begin monitoring your account and executing trades once capital is detected in your exchange.</p>
        </div>
        <button className="alert-close" onClick={handleCloseAlert}>×</button>
      </div>
    </div>
  );

  const renderDashboardStats = () => (
    <div className="dashboard-stats">
      <h2 className="dashboard-title">Your Trading Dashboard</h2>
      
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-header">
            <span className="stat-label">TOTAL BALANCE</span>
          </div>
          <div className="stat-value">$6,500</div>
          <div className="stat-change positive">+$1,500 Today</div>
        </div>

        <div className="stat-card">
          <div className="stat-header">
            <span className="stat-label">PENDING IN TRADE</span>
          </div>
          <div className="stat-value">$3,700</div>
          <div className="stat-change">$0 Today</div>
        </div>

        <div className="stat-card">
          <div className="stat-header">
            <span className="stat-label">CAPITAL</span>
          </div>
          <div className="stat-value">$2,185</div>
        </div>

        <div className="stat-card">
          <div className="stat-header">
            <span className="stat-label">PROFIT</span>
          </div>
          <div className="stat-value">$2,185</div>
          <div className="stat-change positive">+$1,500 Today</div>
        </div>

        <div className="stat-card">
          <div className="stat-header">
            <span className="stat-label">LOSS</span>
          </div>
          <div className="stat-value">$185</div>
          <div className="stat-change negative">-$0 Today</div>
        </div>

        <div className="stat-card">
          <div className="stat-header">
            <span className="stat-label">PROFIT ANTICIPATION</span>
          </div>
          <div className="stat-value">$2,185</div>
        </div>
      </div>
    </div>
  );

  return (
    <Layout>
      <div className="trading-dashboard">
        <div className="dashboard-header">
          <button className="go-back-btn" onClick={handleGoBack}>
            ← Go Back
          </button>
          <h1 className="dashboard-main-title">ESVC Trade Challenge</h1>
        </div>

        {renderProgressBar()}

        {showSuccessAlert && renderSuccessAlert()}

        <div className="dashboard-content">
          {renderDashboardStats()}
        </div>

        {/* More Than Trading Section */}
        <div className="more-than-trading">
          <div className="more-than-trading-content">
            <h2 className="more-than-trading-title">More Than Trading. Fueling Innovation</h2>
            <p className="more-than-trading-text">
              Your trading journey with us is just the beginning. Stake ESVC to earn daily ROI and unlock the 
              chance to pitch your own startup ideas for funding. We reinvest a portion of our platform's profit to 
              support bold solutions from our staking community.
            </p>
            <div className="more-than-trading-buttons">
              <button className="start-staking-btn">
                Start Staking Now 🚀
              </button>
              <button className="get-funded-btn">Get Funded</button>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default TradingDashboard;
