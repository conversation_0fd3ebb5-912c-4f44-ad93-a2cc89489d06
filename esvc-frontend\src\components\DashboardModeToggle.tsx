import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDemoState, DashboardMode } from '../context/DemoStateContext';
import '../styles/components/DashboardModeToggle.css';

// Import icons
import userIcon from '../assets/overview.png';
import treasuryIcon from '../assets/wallet-money.png';
import tradingIcon from '../assets/chart.png';

interface DashboardModeToggleProps {
  className?: string;
}

const DashboardModeToggle: React.FC<DashboardModeToggleProps> = ({ className = '' }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { dashboardMode, setDashboardMode } = useDemoState();

  const dashboardModes = [
    {
      id: 'user' as DashboardMode,
      label: 'User Dashboard',
      icon: userIcon,
      route: '/user-dashboard',
      description: 'Manage your staking and earnings'
    },
    {
      id: 'treasury' as DashboardMode,
      label: 'Treasury Dashboard',
      icon: treasuryIcon,
      route: '/overview',
      description: 'View treasury insights and analytics'
    },
    {
      id: 'trading' as DashboardMode,
      label: 'Trading Dashboard',
      icon: tradingIcon,
      route: '/trading-dashboard',
      description: 'Monitor your trading performance'
    }
  ];

  // Determine current mode based on route
  const getCurrentMode = (): DashboardMode => {
    const path = location.pathname;
    if (path.startsWith('/user-dashboard')) return 'user';
    if (path === '/trading-dashboard') return 'trading';
    if (['/overview', '/live-reserve', '/daily-transactions', '/real-time-staking', '/startup-funding', '/roi-distribution', '/visual-analytics'].includes(path)) {
      return 'treasury';
    }
    return dashboardMode;
  };

  const currentMode = getCurrentMode();

  const handleModeChange = (mode: DashboardMode, route: string) => {
    setDashboardMode(mode);
    navigate(route);
  };

  return (
    <div className={`dashboard-mode-toggle ${className}`}>
      <div className="mode-toggle-container">
        <div className="mode-toggle-header">
          <h3 className="mode-toggle-title">Dashboard Mode</h3>
          <p className="mode-toggle-subtitle">Switch between different dashboard views</p>
        </div>

        <div className="mode-toggle-options">
          {dashboardModes.map((mode) => (
            <button
              key={mode.id}
              className={`mode-option ${currentMode === mode.id ? 'active' : ''}`}
              onClick={() => handleModeChange(mode.id, mode.route)}
            >
              <div className="mode-option-icon">
                <img src={mode.icon} alt={mode.label} />
              </div>
              <div className="mode-option-content">
                <span className="mode-option-label">{mode.label}</span>
                <span className="mode-option-description">{mode.description}</span>
              </div>
              <div className="mode-option-indicator">
                {currentMode === mode.id && (
                  <div className="active-indicator"></div>
                )}
              </div>
            </button>
          ))}
        </div>

        <div className="mode-toggle-footer">
          <div className="current-mode-info">
            <span className="current-mode-label">Current Mode:</span>
            <span className="current-mode-value">
              {dashboardModes.find(m => m.id === currentMode)?.label}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardModeToggle;
