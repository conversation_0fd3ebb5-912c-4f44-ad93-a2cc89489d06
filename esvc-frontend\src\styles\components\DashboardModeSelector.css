/* Dashboard Mode Selector - Compact Design */
.dashboard-mode-selector {
  font-family: 'Montserrat', sans-serif;
  margin-bottom: 24px;
}

.mode-selector-container {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
}

.mode-selector-header {
  margin-bottom: 12px;
  text-align: center;
}

.mode-selector-title {
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
  opacity: 0.8;
}

.mode-selector-options {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.mode-selector-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
  position: relative;
  overflow: hidden;
}

.mode-selector-option:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: rgba(191, 65, 41, 0.3);
  transform: translateY(-2px);
}

.mode-selector-option.active {
  background: linear-gradient(135deg, rgba(191, 65, 41, 0.2) 0%, rgba(209, 144, 73, 0.2) 100%);
  border-color: #BF4129;
  box-shadow: 0 4px 12px rgba(191, 65, 41, 0.3);
}

.mode-selector-option.active::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(191, 65, 41, 0.1) 0%, rgba(209, 144, 73, 0.1) 100%);
  z-index: -1;
}

.mode-option-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  flex-shrink: 0;
}

.mode-selector-option.active .mode-option-icon {
  background: rgba(191, 65, 41, 0.3);
}

.mode-option-icon img {
  width: 14px;
  height: 14px;
  object-fit: contain;
  filter: brightness(0) invert(1);
}

.mode-option-label {
  font-size: 12px;
  font-weight: 600;
  color: #FFFFFF;
  text-align: center;
  line-height: 1.2;
}

.mode-selector-option.active .mode-option-label {
  color: #FFFFFF;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .dashboard-mode-selector {
    margin-bottom: 16px;
  }

  .mode-selector-container {
    padding: 12px;
  }

  .mode-selector-options {
    gap: 6px;
  }

  .mode-selector-option {
    padding: 8px 12px;
    min-width: 70px;
    gap: 4px;
  }

  .mode-option-icon {
    width: 20px;
    height: 20px;
  }

  .mode-option-icon img {
    width: 12px;
    height: 12px;
  }

  .mode-option-label {
    font-size: 11px;
  }

  .mode-selector-title {
    font-size: 12px;
  }
}

/* Compact variant for header */
.dashboard-mode-selector.compact {
  margin-bottom: 0;
}

.dashboard-mode-selector.compact .mode-selector-container {
  background: rgba(26, 26, 26, 0.9);
  padding: 12px;
  border-radius: 8px;
}

.dashboard-mode-selector.compact .mode-selector-header {
  margin-bottom: 8px;
}

.dashboard-mode-selector.compact .mode-selector-title {
  font-size: 12px;
}

.dashboard-mode-selector.compact .mode-selector-options {
  gap: 6px;
}

.dashboard-mode-selector.compact .mode-selector-option {
  padding: 8px 10px;
  min-width: 60px;
  gap: 4px;
}

.dashboard-mode-selector.compact .mode-option-icon {
  width: 18px;
  height: 18px;
}

.dashboard-mode-selector.compact .mode-option-icon img {
  width: 10px;
  height: 10px;
}

.dashboard-mode-selector.compact .mode-option-label {
  font-size: 10px;
}
