import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useDemoState } from '../context/DemoStateContext';
import '../styles/components/DashboardModeSelector.css';

// Import icons
import userIcon from '../assets/overview.png';
import treasuryIcon from '../assets/wallet-money.png';
import tradingIcon from '../assets/chart.png';

type DashboardMode = 'user' | 'treasury' | 'trading';

interface DashboardModeSelectorProps {
  className?: string;
}

const DashboardModeSelector: React.FC<DashboardModeSelectorProps> = ({ className = '' }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { dashboardMode, setDashboardMode } = useDemoState();

  const dashboardModes = [
    {
      id: 'user' as DashboardMode,
      label: 'User',
      icon: userIcon,
      route: '/user-dashboard',
      description: 'Your staking dashboard'
    },
    {
      id: 'treasury' as DashboardMode,
      label: 'Treasury',
      icon: treasuryIcon,
      route: '/overview',
      description: 'Treasury analytics'
    },
    {
      id: 'trading' as DashboardMode,
      label: 'Trading',
      icon: tradingIcon,
      route: '/trading-dashboard',
      description: 'Trading performance'
    }
  ];

  // Determine current mode based on route
  const getCurrentMode = (): DashboardMode => {
    const path = location.pathname;
    if (path.startsWith('/user-dashboard')) return 'user';
    if (path === '/trading-dashboard') return 'trading';
    if (['/overview', '/live-reserve', '/daily-transactions', '/real-time-staking', '/startup-funding', '/roi-distribution', '/visual-analytics'].includes(path)) {
      return 'treasury';
    }
    return dashboardMode;
  };

  const currentMode = getCurrentMode();

  const handleModeChange = (mode: DashboardMode, route: string) => {
    setDashboardMode(mode);
    navigate(route);
  };

  return (
    <div className={`dashboard-mode-selector ${className}`}>
      <div className="mode-selector-container">
        <div className="mode-selector-header">
          <span className="mode-selector-title">Dashboard Mode</span>
        </div>

        <div className="mode-selector-options">
          {dashboardModes.map((mode) => (
            <button
              key={mode.id}
              className={`mode-selector-option ${currentMode === mode.id ? 'active' : ''}`}
              onClick={() => handleModeChange(mode.id, mode.route)}
              title={mode.description}
            >
              <div className="mode-option-icon">
                <img src={mode.icon} alt={mode.label} />
              </div>
              <span className="mode-option-label">{mode.label}</span>
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DashboardModeSelector;
