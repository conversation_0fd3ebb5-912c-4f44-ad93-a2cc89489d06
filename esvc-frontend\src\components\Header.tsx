import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/components/Header.css';
import logoEsvc from '../assets/logo-esvc.png';

interface HeaderProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const Header: React.FC<HeaderProps> = ({
  onNavigateToSignUp,
  onNavigateToLogin,
  onNavigateToLanding
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const navigate = useNavigate();

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <div className="logo" onClick={onNavigateToLanding} style={{ cursor: 'pointer' }}>
            <img src={logoEsvc} alt="ESVC Logo" className="logo-image" />
          </div>

          {/* Desktop Navigation */}
          <nav className="nav desktop-nav">
            <button onClick={onNavigateToLanding} className="nav-link active">Home</button>
            <a href="#stake" className="nav-link">Stake ESVC</a>
            <a href="#funding" className="nav-link">Get Funding</a>
            <button onClick={() => navigate('/trade-challenge')} className="nav-link">Trade Challenge</button>
            <a href="#contact" className="nav-link">Contact Us</a>
          </nav>

          {/* Desktop Action Buttons */}
          <div className="header-actions desktop-actions">
            <button className="btn-secondary" onClick={onNavigateToLogin}>Login</button>
            <button className="btn-primary" onClick={onNavigateToSignUp}>Get Started</button>
          </div>

          {/* Mobile Hamburger Menu */}
          <button className="mobile-menu-toggle" onClick={toggleMobileMenu}>
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
            <span className="hamburger-line"></span>
          </button>
        </div>

        {/* Mobile Menu */}
        <div className={`mobile-menu ${isMobileMenuOpen ? 'mobile-menu-open' : ''}`}>
          <nav className="mobile-nav">
            <button onClick={onNavigateToLanding} className="mobile-nav-link">Home</button>
            <a href="#stake" className="mobile-nav-link">Stake ESVC</a>
            <a href="#funding" className="mobile-nav-link">Get Funding</a>
            <button onClick={() => navigate('/trade-challenge')} className="mobile-nav-link">Trade Challenge</button>
            <a href="#contact" className="mobile-nav-link">Contact Us</a>
          </nav>
          <div className="mobile-actions">
            <button className="btn-primary mobile-btn" onClick={onNavigateToSignUp}>Get Started</button>
            <button className="mobile-login" onClick={onNavigateToLogin}>Login</button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
